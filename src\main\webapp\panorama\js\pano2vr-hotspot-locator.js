/**
 * Pano2VR热点定位脚本
 *
 * 此脚本用于接收来自父页面的定位指令并调用pano2vr的JavaScript API实现热点定位功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 1.0
 */

(function() {
    'use strict';
    
    // 等待pano2vr加载完成
    var waitForPano = function(callback) {
        var checkInterval = setInterval(function() {
            // 检查pano对象是否存在且已初始化
            if (typeof pano !== 'undefined' && pano && typeof pano.moveTo === 'function') {
                clearInterval(checkInterval);
                callback();
            }
        }, 100); // 每100ms检查一次
        
        // 10秒超时
        setTimeout(function() {
            clearInterval(checkInterval);
        }, 10000);
    };
    
    // 初始化消息监听器
    var initMessageListener = function() {
        window.addEventListener('message', function(event) {
            // 安全检查：验证消息来源（可根据需要调整）
            // if (event.origin !== window.location.origin) return;
            
            var data = event.data;
            if (!data || typeof data !== 'object') return;
            
            // 处理热点定位消息
            if (data.type === 'locateHotspot') {
                handleHotspotLocation(data);
            }
        }, false);
    };
    
    // 处理热点定位
    var handleHotspotLocation = function(data) {
        try {
            var pan = parseFloat(data.pan);
            var tilt = parseFloat(data.tilt);
            var speed = parseFloat(data.speed) || 2.0; // 默认速度
            
            // 验证数据
            if (isNaN(pan) || isNaN(tilt)) {
                return;
            }
            
            // 获取当前FOV，保持不变
            var currentFov = pano.getFov();
            
            // 使用moveTo方法实现平滑过渡
            // moveTo(pan, tilt, fov, speed, roll, projection)
            pano.moveTo(pan, tilt, currentFov, speed);
            
            // 向父页面发送定位完成消息
            setTimeout(function() {
                try {
                    window.parent.postMessage({
                        type: 'hotspotLocationComplete',
                        pan: pan,
                        tilt: tilt,
                        success: true
                    }, '*');
                } catch (e) {
                    // 静默处理错误
                }
            }, speed * 1000 + 100); // 等待动画完成后发送消息
            
        } catch (error) {
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotLocationComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                // 静默处理错误
            }
        }
    };
    

    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            waitForPano(initMessageListener);
        });
    } else {
        waitForPano(initMessageListener);
    }
    
    // 为了兼容性，也在window.onload时尝试初始化
    var originalOnload = window.onload;
    window.onload = function() {
        if (originalOnload) originalOnload();
        waitForPano(initMessageListener);
    };
    
})();
