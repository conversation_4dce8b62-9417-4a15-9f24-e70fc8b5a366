/**
 * Pano2VR热点定位脚本
 * 
 * 此脚本需要被注入到pano2vr生成的HTML文件中，用于接收来自父页面的定位指令
 * 并调用pano2vr的JavaScript API实现热点定位功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */

(function() {
    'use strict';
    
    // 等待pano2vr加载完成
    var waitForPano = function(callback) {
        var checkInterval = setInterval(function() {
            // 检查pano对象是否存在且已初始化
            if (typeof pano !== 'undefined' && pano && typeof pano.moveTo === 'function') {
                clearInterval(checkInterval);
                callback();
            }
        }, 100); // 每100ms检查一次
        
        // 10秒超时
        setTimeout(function() {
            clearInterval(checkInterval);
            console.warn('Pano2VR加载超时，热点定位功能可能不可用');
        }, 10000);
    };
    
    // 初始化消息监听器
    var initMessageListener = function() {
        window.addEventListener('message', function(event) {
            // 安全检查：验证消息来源（可根据需要调整）
            // if (event.origin !== window.location.origin) return;
            
            var data = event.data;
            if (!data || typeof data !== 'object') return;
            
            // 处理热点定位消息
            if (data.type === 'locateHotspot') {
                handleHotspotLocation(data);
            }
        }, false);
        
        console.log('Pano2VR热点定位监听器已初始化');
    };
    
    // 处理热点定位
    var handleHotspotLocation = function(data) {
        try {
            var pan = parseFloat(data.pan);
            var tilt = parseFloat(data.tilt);
            var speed = parseFloat(data.speed) || 2.0; // 默认速度
            
            // 验证数据
            if (isNaN(pan) || isNaN(tilt)) {
                console.error('无效的热点位置数据:', data);
                return;
            }
            
            console.log('开始定位热点 - Pan:', pan, 'Tilt:', tilt, 'Speed:', speed);
            
            // 获取当前FOV，保持不变
            var currentFov = pano.getFov();
            
            // 使用moveTo方法实现平滑过渡
            // moveTo(pan, tilt, fov, speed, roll, projection)
            pano.moveTo(pan, tilt, currentFov, speed);
            
            // 向父页面发送定位完成消息
            setTimeout(function() {
                try {
                    window.parent.postMessage({
                        type: 'hotspotLocationComplete',
                        pan: pan,
                        tilt: tilt,
                        success: true
                    }, '*');
                } catch (e) {
                    console.warn('无法向父页面发送定位完成消息:', e);
                }
            }, speed * 1000 + 100); // 等待动画完成后发送消息
            
        } catch (error) {
            console.error('热点定位失败:', error);
            
            // 向父页面发送错误消息
            try {
                window.parent.postMessage({
                    type: 'hotspotLocationComplete',
                    success: false,
                    error: error.message
                }, '*');
            } catch (e) {
                console.warn('无法向父页面发送错误消息:', e);
            }
        }
    };
    
    // 提供手动定位方法（供调试使用）
    window.locateToHotspot = function(pan, tilt, speed) {
        handleHotspotLocation({
            pan: pan,
            tilt: tilt,
            speed: speed || 2.0
        });
    };
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            waitForPano(initMessageListener);
        });
    } else {
        waitForPano(initMessageListener);
    }
    
    // 为了兼容性，也在window.onload时尝试初始化
    var originalOnload = window.onload;
    window.onload = function() {
        if (originalOnload) originalOnload();
        waitForPano(initMessageListener);
    };
    
})();

/**
 * 使用说明：
 * 
 * 1. 将此脚本添加到pano2vr生成的HTML文件中，在</body>标签之前
 * 2. 确保在pano2vr初始化完成后加载此脚本
 * 3. 从父页面发送消息格式：
 *    {
 *        type: 'locateHotspot',
 *        pan: 90.0,    // pan角度
 *        tilt: 0.0,    // tilt角度
 *        speed: 2.0    // 过渡速度（可选，默认2.0）
 *    }
 * 
 * 4. 脚本会自动处理定位并发送完成消息给父页面
 * 
 * 调试方法：
 * - 在浏览器控制台中调用 locateToHotspot(pan, tilt, speed) 进行测试
 * - 检查控制台日志了解脚本运行状态
 */
