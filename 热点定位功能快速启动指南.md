# 热点定位功能快速启动指南

## 功能验证步骤

### 1. 编译和启动项目
```bash
# 确保项目编译无错误
mvn clean compile

# 启动项目
mvn spring-boot:run
# 或使用现有的启动脚本
```

### 2. 访问测试页面
打开浏览器访问：
```
http://localhost:8080/DataPackageManagement/panorama/test-hotspot-location.html
```

### 3. 测试基本定位功能
1. 在测试页面中，使用预设位置按钮测试：
   - 点击"正前方 (0°, 0°)"
   - 点击"右侧 (90°, 0°)"
   - 点击"左侧 (-90°, 0°)"
   - 观察全景图是否正确定位

2. 手动输入测试：
   - 在Pan角度输入框输入：45
   - 在Tilt角度输入框输入：30
   - 点击"测试定位"按钮
   - 观察全景图过渡效果

### 4. 测试完整功能流程
1. 访问主编辑页面：
   ```
   http://localhost:8080/DataPackageManagement/panorama-editor.html
   ```

2. 创建新任务：
   - 点击"创建任务"按钮
   - 填写任务信息并保存

3. 上传全景图文件：
   - 准备一个pano2vr生成的ZIP文件
   - 点击"上传全景图ZIP"按钮上传
   - 等待解压和处理完成

4. 测试热点定位：
   - 在热点编辑表格中找到热点记录
   - 点击操作列中的"定位"按钮
   - 观察右侧全景图是否自动定位到热点位置

## 故障排除

### 问题1：测试页面无法加载全景图
**解决方案：**
- 确保 `/quanjing/index.html` 文件存在
- 检查文件路径是否正确
- 查看浏览器控制台是否有错误信息

### 问题2：点击定位按钮无反应
**检查步骤：**
1. 打开浏览器开发者工具
2. 查看Console标签页是否有错误信息
3. 检查Network标签页，确认API请求是否成功
4. 验证热点数据中是否包含PAN和TILT字段

### 问题3：定位位置不准确
**可能原因：**
- 数据库中PAN/TILT数据格式不正确
- 角度单位不匹配（应为度数，不是弧度）
- pano2vr版本兼容性问题

### 问题4：过渡效果异常
**调整方法：**
- 修改速度参数（建议1.0-3.0）
- 检查pano2vr的moveTo API是否正常工作

## 开发调试

### 1. 查看定位脚本是否注入成功
1. 上传ZIP文件后，检查解压目录中的index.html
2. 搜索"热点定位功能脚本"关键字
3. 确认脚本已正确注入

### 2. 调试iframe通信
在浏览器控制台中执行：
```javascript
// 手动发送定位消息
document.getElementById('panoramaFrame').contentWindow.postMessage({
    type: 'locateHotspot',
    pan: 90,
    tilt: 0,
    speed: 2.0
}, '*');
```

### 3. 检查数据库数据
```sql
-- 查看热点数据
SELECT HOTSPOT_ID, ORIGINAL_TITLE, PAN, TILT 
FROM PANORAMA_HOTSPOT 
WHERE TASK_ID = [你的任务ID];

-- 检查PAN和TILT是否为有效数值
SELECT * FROM PANORAMA_HOTSPOT 
WHERE PAN IS NOT NULL AND TILT IS NOT NULL;
```

## 性能优化建议

### 1. 大量热点处理
- 热点表格使用分页加载
- 避免一次性加载过多热点数据

### 2. 定位响应速度
- 调整过渡速度参数
- 考虑预加载热点位置数据

### 3. 内存管理
- 定期清理临时文件
- 避免iframe重复加载

## 扩展功能开发

### 1. 批量定位
可以扩展支持选择多个热点进行连续定位：
```javascript
function batchLocate(hotspotIds) {
    // 实现批量定位逻辑
}
```

### 2. 定位动画效果
可以添加更多过渡动画效果：
- 缓动函数
- 路径规划
- 视角跟随

### 3. 热点高亮
定位后高亮显示热点：
- 修改热点样式
- 添加闪烁效果
- 显示热点信息

## 注意事项

1. **浏览器兼容性**：确保使用支持postMessage的现代浏览器
2. **CORS策略**：如果遇到跨域问题，检查服务器CORS配置
3. **文件权限**：确保上传目录有读写权限
4. **数据精度**：PAN和TILT建议保留1-2位小数

## 联系支持

如果遇到问题，请检查：
1. 浏览器控制台错误信息
2. 服务器日志文件
3. 数据库连接状态
4. 文件上传和解压是否成功

---

**版本：** v1.0  
**更新时间：** 2025-01-27  
**兼容性：** JDK 1.8+, Spring Boot 2.7.18
