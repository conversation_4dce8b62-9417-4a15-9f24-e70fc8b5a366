<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe通信调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-panel {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .debug-panel h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .iframe-container {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .log-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .log-success {
            background: #d4edda;
            color: #155724;
        }
        .log-error {
            background: #f8d7da;
            color: #721c24;
        }
        .log-warning {
            background: #fff3cd;
            color: #856404;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
        .input-group input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iframe通信调试页面</h1>
        
        <!-- 基本测试面板 -->
        <div class="debug-panel">
            <h3>基本通信测试</h3>
            <button class="btn" onclick="testBasicCommunication()">测试基本通信</button>
            <button class="btn" onclick="checkIframeStatus()">检查iframe状态</button>
            <button class="btn" onclick="testPanoObject()">测试pano对象</button>
            <button class="btn btn-danger" onclick="clearLog()">清空日志</button>
        </div>

        <!-- 定位测试面板 -->
        <div class="debug-panel">
            <h3>热点定位测试</h3>
            <div class="input-group">
                <label>Pan角度:</label>
                <input type="number" id="panInput" value="0" step="0.1" min="-180" max="180">
            </div>
            <div class="input-group">
                <label>Tilt角度:</label>
                <input type="number" id="tiltInput" value="0" step="0.1" min="-90" max="90">
            </div>
            <div class="input-group">
                <label>速度:</label>
                <input type="number" id="speedInput" value="2.0" step="0.1" min="0.1" max="10">
            </div>
            <button class="btn btn-success" onclick="testHotspotLocation()">测试热点定位</button>
        </div>

        <!-- iframe显示区域 -->
        <div class="debug-panel">
            <h3>全景图iframe</h3>
            <div class="iframe-container">
                <iframe id="panoramaFrame" src="/quanjing/index.html"></iframe>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="log-panel">
            <h4>调试日志</h4>
            <div id="logContainer">
                <div class="log-entry log-info">调试页面已加载</div>
            </div>
        </div>
    </div>

    <script>
        // 日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空');
        }

        // 测试基本通信
        function testBasicCommunication() {
            addLog('开始测试基本通信...');
            const iframe = document.getElementById('panoramaFrame');
            
            if (!iframe) {
                addLog('错误：找不到iframe元素', 'error');
                return;
            }

            if (!iframe.contentWindow) {
                addLog('错误：iframe.contentWindow不可用', 'error');
                return;
            }

            try {
                const testMessage = {
                    type: 'test',
                    message: 'Hello from parent window',
                    timestamp: Date.now()
                };
                
                addLog('发送测试消息: ' + JSON.stringify(testMessage));
                iframe.contentWindow.postMessage(testMessage, '*');
                addLog('测试消息发送成功', 'success');
            } catch (error) {
                addLog('发送测试消息失败: ' + error.message, 'error');
            }
        }

        // 检查iframe状态
        function checkIframeStatus() {
            addLog('检查iframe状态...');
            const iframe = document.getElementById('panoramaFrame');
            
            const status = {
                exists: !!iframe,
                src: iframe ? iframe.src : 'N/A',
                contentWindow: iframe ? !!iframe.contentWindow : false,
                readyState: iframe ? iframe.readyState : 'N/A'
            };
            
            addLog('iframe状态: ' + JSON.stringify(status, null, 2));
        }

        // 测试pano对象
        function testPanoObject() {
            addLog('测试pano对象...');
            const iframe = document.getElementById('panoramaFrame');
            
            if (!iframe || !iframe.contentWindow) {
                addLog('iframe不可用', 'error');
                return;
            }

            try {
                const testMessage = {
                    type: 'checkPano',
                    timestamp: Date.now()
                };
                
                addLog('发送pano检查消息');
                iframe.contentWindow.postMessage(testMessage, '*');
            } catch (error) {
                addLog('发送pano检查消息失败: ' + error.message, 'error');
            }
        }

        // 测试热点定位
        function testHotspotLocation() {
            const pan = parseFloat(document.getElementById('panInput').value);
            const tilt = parseFloat(document.getElementById('tiltInput').value);
            const speed = parseFloat(document.getElementById('speedInput').value);
            
            addLog(`测试热点定位 - Pan: ${pan}, Tilt: ${tilt}, Speed: ${speed}`);
            
            const iframe = document.getElementById('panoramaFrame');
            if (!iframe || !iframe.contentWindow) {
                addLog('iframe不可用', 'error');
                return;
            }

            const message = {
                type: 'locateHotspot',
                pan: pan,
                tilt: tilt,
                speed: speed
            };

            try {
                addLog('发送定位消息: ' + JSON.stringify(message));
                iframe.contentWindow.postMessage(message, '*');
                addLog('定位消息发送成功', 'success');
            } catch (error) {
                addLog('发送定位消息失败: ' + error.message, 'error');
            }
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            addLog('收到iframe消息: ' + JSON.stringify(event.data));
            
            if (event.data && event.data.type) {
                switch(event.data.type) {
                    case 'hotspotLocationComplete':
                        if (event.data.success) {
                            addLog(`定位完成 - Pan: ${event.data.pan}, Tilt: ${event.data.tilt}`, 'success');
                        } else {
                            addLog(`定位失败: ${event.data.error}`, 'error');
                        }
                        break;
                    case 'panoStatus':
                        addLog('pano对象状态: ' + JSON.stringify(event.data), 'info');
                        break;
                    default:
                        addLog('未知消息类型: ' + event.data.type, 'warning');
                }
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            addLog('调试页面初始化完成');
            
            // 等待iframe加载
            const iframe = document.getElementById('panoramaFrame');
            iframe.addEventListener('load', function() {
                addLog('iframe加载完成');
                setTimeout(function() {
                    checkIframeStatus();
                }, 1000);
            });
        });
    </script>
</body>
</html>
