<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热点定位功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-panel {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-panel h3 {
            margin-top: 0;
            color: #333;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
        .input-group input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .log-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .log-success {
            background: #d4edda;
            color: #155724;
        }
        .log-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>热点定位功能测试</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-panel">
            <h3>定位测试控制</h3>
            <div class="input-group">
                <label>Pan角度:</label>
                <input type="number" id="panInput" value="0" step="0.1" min="-180" max="180">
                <span>(-180 到 180)</span>
            </div>
            <div class="input-group">
                <label>Tilt角度:</label>
                <input type="number" id="tiltInput" value="0" step="0.1" min="-90" max="90">
                <span>(-90 到 90)</span>
            </div>
            <div class="input-group">
                <label>速度:</label>
                <input type="number" id="speedInput" value="2.0" step="0.1" min="0.1" max="10">
                <span>(0.1 到 10，数值越大越快)</span>
            </div>
            <div>
                <button class="btn" onclick="testHotspotLocation()">测试定位</button>
                <button class="btn btn-success" onclick="loadPresetLocations()">加载预设位置</button>
                <button class="btn" onclick="clearLog()">清空日志</button>
            </div>
        </div>

        <!-- 预设位置测试 -->
        <div class="test-panel">
            <h3>预设位置快速测试</h3>
            <button class="btn" onclick="testLocation(0, 0, 2.0)">正前方 (0°, 0°)</button>
            <button class="btn" onclick="testLocation(90, 0, 2.0)">右侧 (90°, 0°)</button>
            <button class="btn" onclick="testLocation(-90, 0, 2.0)">左侧 (-90°, 0°)</button>
            <button class="btn" onclick="testLocation(180, 0, 2.0)">后方 (180°, 0°)</button>
            <button class="btn" onclick="testLocation(0, 45, 2.0)">向上 (0°, 45°)</button>
            <button class="btn" onclick="testLocation(0, -45, 2.0)">向下 (0°, -45°)</button>
        </div>

        <!-- 全景图显示区域 -->
        <div class="test-panel">
            <h3>全景图预览</h3>
            <div class="iframe-container">
                <iframe id="panoramaFrame" src="/DataPackageManagement/quanjing/index.html"></iframe>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="log-panel">
            <h4>操作日志</h4>
            <div id="logContainer">
                <div class="log-entry log-info">测试页面已加载，可以开始测试热点定位功能</div>
            </div>
        </div>
    </div>

    <script>
        // 日志函数
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            addLog('日志已清空');
        }

        // 测试热点定位
        function testHotspotLocation() {
            const pan = parseFloat(document.getElementById('panInput').value);
            const tilt = parseFloat(document.getElementById('tiltInput').value);
            const speed = parseFloat(document.getElementById('speedInput').value);
            
            testLocation(pan, tilt, speed);
        }

        // 执行定位测试
        function testLocation(pan, tilt, speed) {
            addLog(`开始测试定位 - Pan: ${pan}°, Tilt: ${tilt}°, Speed: ${speed}`);
            
            const iframe = document.getElementById('panoramaFrame');
            if (!iframe || !iframe.contentWindow) {
                addLog('错误：无法访问iframe', 'error');
                return;
            }

            const message = {
                type: 'locateHotspot',
                pan: pan,
                tilt: tilt,
                speed: speed
            };

            try {
                iframe.contentWindow.postMessage(message, '*');
                addLog(`定位消息已发送`, 'success');
            } catch (error) {
                addLog(`发送定位消息失败: ${error.message}`, 'error');
            }
        }

        // 加载预设位置到输入框
        function loadPresetLocations() {
            // 这里可以加载一些常用的热点位置
            addLog('预设位置功能可以根据实际需求添加');
        }

        // 监听来自iframe的消息
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'hotspotLocationComplete') {
                if (event.data.success) {
                    addLog(`定位完成 - Pan: ${event.data.pan}°, Tilt: ${event.data.tilt}°`, 'success');
                } else {
                    addLog(`定位失败: ${event.data.error || '未知错误'}`, 'error');
                }
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            addLog('测试页面初始化完成');
            
            // 等待iframe加载
            const iframe = document.getElementById('panoramaFrame');
            iframe.addEventListener('load', function() {
                addLog('全景图iframe加载完成，可以开始测试');
            });
        });
    </script>
</body>
</html>
