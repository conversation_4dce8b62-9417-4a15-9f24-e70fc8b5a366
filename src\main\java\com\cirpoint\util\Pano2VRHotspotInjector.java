package com.cirpoint.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Pano2VR热点定位脚本注入工具
 * 
 * 用于在pano2vr生成的HTML文件中自动注入热点定位功能脚本
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Slf4j
public class Pano2VRHotspotInjector {
    
    /**
     * 热点定位脚本标签 - 引用外部JS文件
     */
    private static final String HOTSPOT_LOCATOR_SCRIPT_TAG =
        "\t\t<!-- 热点定位功能脚本 -->\n" +
        "\t\t<script type=\"text/javascript\" src=\"/panorama/js/pano2vr-hotspot-locator.js\"></script>";
    
    /**
     * 检查HTML文件是否已经注入了热点定位脚本
     */
    public static boolean hasHotspotLocatorScript(String htmlContent) {
        return htmlContent.contains("热点定位功能脚本") ||
               htmlContent.contains("pano2vr-hotspot-locator.js");
    }

    /**
     * 从HTML文件中移除热点定位脚本标签
     */
    public static String removeHotspotLocatorScript(String htmlContent) {
        if (!hasHotspotLocatorScript(htmlContent)) {
            log.info("HTML文件中没有热点定位脚本，无需移除");
            return htmlContent;
        }

        // 移除注入的脚本标签和注释
        String result = htmlContent;

        // 移除注释和脚本标签（支持多行匹配）
        Pattern pattern = Pattern.compile(
            "\\s*<!-- 热点定位功能脚本 -->\\s*\\n?" +
            "\\s*<script[^>]*src=\"[^\"]*pano2vr-hotspot-locator\\.js\"[^>]*></script>\\s*\\n?",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );

        result = pattern.matcher(result).replaceAll("");

        // 清理可能的多余空行
        result = result.replaceAll("\\n\\s*\\n\\s*\\n", "\n\n");

        log.info("成功移除热点定位脚本标签");
        return result;
    }
    
    /**
     * 为HTML文件注入热点定位脚本
     */
    public static String injectHotspotLocatorScript(String htmlContent) {
        if (hasHotspotLocatorScript(htmlContent)) {
            log.info("HTML文件已包含热点定位脚本，跳过注入");
            return htmlContent;
        }

        // 查找</script>标签的位置，在最后一个</script>后插入我们的脚本
        // 修改正则表达式，支持</script>和</body>之间有其他内容
        Pattern pattern = Pattern.compile("(.*</script>)(.*?)(</body>)", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            String beforeScript = matcher.group(1);
            String middleContent = matcher.group(2);
            String afterScript = matcher.group(3);

            String result = beforeScript + "\n\n" + HOTSPOT_LOCATOR_SCRIPT_TAG + "\n" + middleContent + afterScript;
            log.info("成功注入热点定位脚本标签");
            return result;
        } else {
            log.warn("未找到合适的位置注入热点定位脚本，尝试在</body>前插入");

            // 备用方案：直接在</body>前插入
            Pattern bodyPattern = Pattern.compile("(.*)(</body>)", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
            Matcher bodyMatcher = bodyPattern.matcher(htmlContent);

            if (bodyMatcher.find()) {
                String beforeBody = bodyMatcher.group(1);
                String bodyTag = bodyMatcher.group(2);

                String result = beforeBody + "\n\n" + HOTSPOT_LOCATOR_SCRIPT_TAG + "\n\n" + bodyTag;
                log.info("在</body>前成功注入热点定位脚本标签");
                return result;
            } else {
                log.error("无法找到</body>标签，注入失败");
                return htmlContent;
            }
        }
    }
    
    /**
     * 处理指定目录下的所有HTML文件，注入热点定位脚本
     */
    public static void processDirectory(String directoryPath) {
        try {
            Path dir = Paths.get(directoryPath);
            if (!Files.exists(dir) || !Files.isDirectory(dir)) {
                log.error("目录不存在或不是有效目录: {}", directoryPath);
                return;
            }

            Files.walk(dir)
                .filter(path -> path.toString().toLowerCase().endsWith(".html"))
                .forEach(Pano2VRHotspotInjector::processHtmlFile);

        } catch (Exception e) {
            log.error("处理目录失败: {}", directoryPath, e);
        }
    }

    /**
     * 清理指定目录下的所有HTML文件，移除热点定位脚本
     */
    public static void cleanDirectory(String directoryPath) {
        try {
            Path dir = Paths.get(directoryPath);
            if (!Files.exists(dir) || !Files.isDirectory(dir)) {
                log.error("目录不存在或不是有效目录: {}", directoryPath);
                return;
            }

            log.info("开始清理目录中的热点定位脚本: {}", directoryPath);
            Files.walk(dir)
                .filter(path -> path.toString().toLowerCase().endsWith(".html"))
                .forEach(Pano2VRHotspotInjector::cleanHtmlFile);

        } catch (Exception e) {
            log.error("清理目录失败: {}", directoryPath, e);
        }
    }
    
    /**
     * 处理单个HTML文件 - 注入脚本
     */
    public static void processHtmlFile(Path htmlFile) {
        try {
            log.info("处理HTML文件: {}", htmlFile);

            // 读取文件内容 - JDK 1.8兼容方式
            String content = readFileContent(htmlFile.toFile());

            // 注入脚本
            String modifiedContent = injectHotspotLocatorScript(content);

            // 如果内容有变化，写回文件
            if (!content.equals(modifiedContent)) {
                writeFileContent(htmlFile.toFile(), modifiedContent);
                log.info("已更新HTML文件: {}", htmlFile);
            }

        } catch (Exception e) {
            log.error("处理HTML文件失败: {}", htmlFile, e);
        }
    }

    /**
     * 清理单个HTML文件 - 移除脚本
     */
    public static void cleanHtmlFile(Path htmlFile) {
        try {
            log.info("清理HTML文件: {}", htmlFile);

            // 读取文件内容 - JDK 1.8兼容方式
            String content = readFileContent(htmlFile.toFile());

            // 移除脚本
            String cleanedContent = removeHotspotLocatorScript(content);

            // 如果内容有变化，写回文件
            if (!content.equals(cleanedContent)) {
                writeFileContent(htmlFile.toFile(), cleanedContent);
                log.info("已清理HTML文件: {}", htmlFile);
            }

        } catch (Exception e) {
            log.error("清理HTML文件失败: {}", htmlFile, e);
        }
    }

    /**
     * 读取文件内容 - JDK 1.8兼容方法
     */
    private static String readFileContent(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    /**
     * 写入文件内容 - JDK 1.8兼容方法
     */
    private static void writeFileContent(File file, String content) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8))) {
            writer.write(content);
        }
    }
}
