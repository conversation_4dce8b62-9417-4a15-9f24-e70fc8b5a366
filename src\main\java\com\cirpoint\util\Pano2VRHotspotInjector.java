package com.cirpoint.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Pano2VR热点定位脚本注入工具
 * 
 * 用于在pano2vr生成的HTML文件中自动注入热点定位功能脚本
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Slf4j
public class Pano2VRHotspotInjector {
    
    /**
     * 热点定位脚本内容
     */
    private static final String HOTSPOT_LOCATOR_SCRIPT = 
        "\t\t<!-- 热点定位功能脚本 -->\n" +
        "\t\t<script type=\"text/javascript\">\n" +
        "\t\t\t/**\n" +
        "\t\t\t * Pano2VR热点定位脚本 - 内联版本\n" +
        "\t\t\t * 用于接收来自父页面的定位指令并调用pano2vr API实现热点定位\n" +
        "\t\t\t */\n" +
        "\t\t\t(function() {\n" +
        "\t\t\t\t'use strict';\n" +
        "\t\t\t\t\n" +
        "\t\t\t\t// 等待pano2vr加载完成\n" +
        "\t\t\t\tvar waitForPano = function(callback) {\n" +
        "\t\t\t\t\tvar checkInterval = setInterval(function() {\n" +
        "\t\t\t\t\t\t// 检查pano对象是否存在且已初始化\n" +
        "\t\t\t\t\t\tif (typeof pano !== 'undefined' && pano && typeof pano.moveTo === 'function') {\n" +
        "\t\t\t\t\t\t\tclearInterval(checkInterval);\n" +
        "\t\t\t\t\t\t\tcallback();\n" +
        "\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t}, 100); // 每100ms检查一次\n" +
        "\t\t\t\t\t\n" +
        "\t\t\t\t\t// 10秒超时\n" +
        "\t\t\t\t\tsetTimeout(function() {\n" +
        "\t\t\t\t\t\tclearInterval(checkInterval);\n" +
        "\t\t\t\t\t\tconsole.warn('Pano2VR加载超时，热点定位功能可能不可用');\n" +
        "\t\t\t\t\t}, 10000);\n" +
        "\t\t\t\t};\n" +
        "\t\t\t\t\n" +
        "\t\t\t\t// 初始化消息监听器\n" +
        "\t\t\t\tvar initMessageListener = function() {\n" +
        "\t\t\t\t\twindow.addEventListener('message', function(event) {\n" +
        "\t\t\t\t\t\tconsole.log('收到postMessage消息:', event.data);\n" +
        "\t\t\t\t\t\tvar data = event.data;\n" +
        "\t\t\t\t\t\tif (!data || typeof data !== 'object') {\n" +
        "\t\t\t\t\t\t\tconsole.log('消息数据无效，忽略');\n" +
        "\t\t\t\t\t\t\treturn;\n" +
        "\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\t// 处理不同类型的消息\n" +
        "\t\t\t\t\t\tswitch(data.type) {\n" +
        "\t\t\t\t\t\t\tcase 'locateHotspot':\n" +
        "\t\t\t\t\t\t\t\tconsole.log('处理热点定位消息:', data);\n" +
        "\t\t\t\t\t\t\t\thandleHotspotLocation(data);\n" +
        "\t\t\t\t\t\t\t\tbreak;\n" +
        "\t\t\t\t\t\t\tcase 'test':\n" +
        "\t\t\t\t\t\t\t\tconsole.log('收到测试消息:', data);\n" +
        "\t\t\t\t\t\t\t\twindow.parent.postMessage({type: 'testResponse', message: 'Hello from iframe', original: data}, '*');\n" +
        "\t\t\t\t\t\t\t\tbreak;\n" +
        "\t\t\t\t\t\t\tcase 'checkPano':\n" +
        "\t\t\t\t\t\t\t\tconsole.log('检查pano对象状态');\n" +
        "\t\t\t\t\t\t\t\tvar panoStatus = {\n" +
        "\t\t\t\t\t\t\t\t\ttype: 'panoStatus',\n" +
        "\t\t\t\t\t\t\t\t\tpanoExists: typeof pano !== 'undefined',\n" +
        "\t\t\t\t\t\t\t\t\tmoveToExists: typeof pano !== 'undefined' && typeof pano.moveTo === 'function',\n" +
        "\t\t\t\t\t\t\t\t\tgetFovExists: typeof pano !== 'undefined' && typeof pano.getFov === 'function',\n" +
        "\t\t\t\t\t\t\t\t\ttimestamp: Date.now()\n" +
        "\t\t\t\t\t\t\t\t};\n" +
        "\t\t\t\t\t\t\t\tif (typeof pano !== 'undefined') {\n" +
        "\t\t\t\t\t\t\t\t\ttry {\n" +
        "\t\t\t\t\t\t\t\t\t\tpanoStatus.currentPan = pano.getPan();\n" +
        "\t\t\t\t\t\t\t\t\t\tpanoStatus.currentTilt = pano.getTilt();\n" +
        "\t\t\t\t\t\t\t\t\t\tpanoStatus.currentFov = pano.getFov();\n" +
        "\t\t\t\t\t\t\t\t\t} catch(e) {\n" +
        "\t\t\t\t\t\t\t\t\t\tpanoStatus.error = e.message;\n" +
        "\t\t\t\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t\t\t\twindow.parent.postMessage(panoStatus, '*');\n" +
        "\t\t\t\t\t\t\t\tbreak;\n" +
        "\t\t\t\t\t\t\tdefault:\n" +
        "\t\t\t\t\t\t\t\tconsole.log('未知消息类型:', data.type);\n" +
        "\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t}, false);\n" +
        "\t\t\t\t\t\n" +
        "\t\t\t\t\tconsole.log('Pano2VR热点定位监听器已初始化');\n" +
        "\t\t\t\t};\n" +
        "\t\t\t\t\n" +
        "\t\t\t\t// 处理热点定位\n" +
        "\t\t\t\tvar handleHotspotLocation = function(data) {\n" +
        "\t\t\t\t\ttry {\n" +
        "\t\t\t\t\t\tconsole.log('开始处理热点定位，检查pano对象状态...');\n" +
        "\t\t\t\t\t\tconsole.log('pano对象存在:', typeof pano !== 'undefined');\n" +
        "\t\t\t\t\t\tconsole.log('moveTo方法存在:', typeof pano.moveTo === 'function');\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\tif (typeof pano === 'undefined' || !pano) {\n" +
        "\t\t\t\t\t\t\tconsole.error('pano对象未定义或为空');\n" +
        "\t\t\t\t\t\t\tthrow new Error('pano对象未定义');\n" +
        "\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\tif (typeof pano.moveTo !== 'function') {\n" +
        "\t\t\t\t\t\t\tconsole.error('pano.moveTo方法不存在');\n" +
        "\t\t\t\t\t\t\tthrow new Error('pano.moveTo方法不存在');\n" +
        "\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\tvar pan = parseFloat(data.pan);\n" +
        "\t\t\t\t\t\tvar tilt = parseFloat(data.tilt);\n" +
        "\t\t\t\t\t\tvar speed = parseFloat(data.speed) || 2.0; // 默认速度\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\t// 验证数据\n" +
        "\t\t\t\t\t\tif (isNaN(pan) || isNaN(tilt)) {\n" +
        "\t\t\t\t\t\t\tconsole.error('无效的热点位置数据:', data);\n" +
        "\t\t\t\t\t\t\tthrow new Error('无效的热点位置数据');\n" +
        "\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\tconsole.log('开始定位热点 - Pan:', pan, 'Tilt:', tilt, 'Speed:', speed);\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\t// 获取当前FOV，保持不变\n" +
        "\t\t\t\t\t\tvar currentFov = pano.getFov();\n" +
        "\t\t\t\t\t\tconsole.log('当前FOV:', currentFov);\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\t// 使用moveTo方法实现平滑过渡\n" +
        "\t\t\t\t\t\tconsole.log('调用pano.moveTo...');\n" +
        "\t\t\t\t\t\tpano.moveTo(pan, tilt, currentFov, speed);\n" +
        "\t\t\t\t\t\tconsole.log('pano.moveTo调用完成');\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\t// 向父页面发送定位完成消息\n" +
        "\t\t\t\t\t\tsetTimeout(function() {\n" +
        "\t\t\t\t\t\t\ttry {\n" +
        "\t\t\t\t\t\t\t\twindow.parent.postMessage({\n" +
        "\t\t\t\t\t\t\t\t\ttype: 'hotspotLocationComplete',\n" +
        "\t\t\t\t\t\t\t\t\tpan: pan,\n" +
        "\t\t\t\t\t\t\t\t\ttilt: tilt,\n" +
        "\t\t\t\t\t\t\t\t\tsuccess: true\n" +
        "\t\t\t\t\t\t\t\t}, '*');\n" +
        "\t\t\t\t\t\t\t} catch (e) {\n" +
        "\t\t\t\t\t\t\t\tconsole.warn('无法向父页面发送定位完成消息:', e);\n" +
        "\t\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t\t}, speed * 1000 + 100); // 等待动画完成后发送消息\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t} catch (error) {\n" +
        "\t\t\t\t\t\tconsole.error('热点定位失败:', error);\n" +
        "\t\t\t\t\t\t\n" +
        "\t\t\t\t\t\t// 向父页面发送错误消息\n" +
        "\t\t\t\t\t\ttry {\n" +
        "\t\t\t\t\t\t\twindow.parent.postMessage({\n" +
        "\t\t\t\t\t\t\t\ttype: 'hotspotLocationComplete',\n" +
        "\t\t\t\t\t\t\t\tsuccess: false,\n" +
        "\t\t\t\t\t\t\t\terror: error.message\n" +
        "\t\t\t\t\t\t\t}, '*');\n" +
        "\t\t\t\t\t\t} catch (e) {\n" +
        "\t\t\t\t\t\t\tconsole.warn('无法向父页面发送错误消息:', e);\n" +
        "\t\t\t\t\t\t}\n" +
        "\t\t\t\t\t}\n" +
        "\t\t\t\t};\n" +
        "\t\t\t\t\n" +
        "\t\t\t\t// 提供手动定位方法（供调试使用）\n" +
        "\t\t\t\twindow.locateToHotspot = function(pan, tilt, speed) {\n" +
        "\t\t\t\t\thandleHotspotLocation({\n" +
        "\t\t\t\t\t\tpan: pan,\n" +
        "\t\t\t\t\t\ttilt: tilt,\n" +
        "\t\t\t\t\t\tspeed: speed || 2.0\n" +
        "\t\t\t\t\t});\n" +
        "\t\t\t\t};\n" +
        "\t\t\t\t\n" +
        "\t\t\t\t// 页面加载完成后初始化\n" +
        "\t\t\t\tif (document.readyState === 'loading') {\n" +
        "\t\t\t\t\tdocument.addEventListener('DOMContentLoaded', function() {\n" +
        "\t\t\t\t\t\twaitForPano(initMessageListener);\n" +
        "\t\t\t\t\t});\n" +
        "\t\t\t\t} else {\n" +
        "\t\t\t\t\twaitForPano(initMessageListener);\n" +
        "\t\t\t\t}\n" +
        "\t\t\t\t\n" +
        "\t\t\t\t// 为了兼容性，也在window.onload时尝试初始化\n" +
        "\t\t\t\tvar originalOnload = window.onload;\n" +
        "\t\t\t\twindow.onload = function() {\n" +
        "\t\t\t\t\tif (originalOnload) originalOnload();\n" +
        "\t\t\t\t\twaitForPano(initMessageListener);\n" +
        "\t\t\t\t};\n" +
        "\t\t\t\t\n" +
        "\t\t\t})();\n" +
        "\t\t</script>";
    
    /**
     * 检查HTML文件是否已经注入了热点定位脚本
     */
    public static boolean hasHotspotLocatorScript(String htmlContent) {
        return htmlContent.contains("热点定位功能脚本") || 
               htmlContent.contains("hotspotLocationComplete");
    }
    
    /**
     * 为HTML文件注入热点定位脚本
     */
    public static String injectHotspotLocatorScript(String htmlContent) {
        if (hasHotspotLocatorScript(htmlContent)) {
            log.info("HTML文件已包含热点定位脚本，跳过注入");
            return htmlContent;
        }
        
        // 查找</script>标签的位置，在最后一个</script>后插入我们的脚本
        // 修改正则表达式，支持</script>和</body>之间有其他内容
        Pattern pattern = Pattern.compile("(.*</script>)(.*?)(</body>)", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlContent);

        if (matcher.find()) {
            String beforeScript = matcher.group(1);
            String middleContent = matcher.group(2);
            String afterScript = matcher.group(3);

            String result = beforeScript + "\n\n" + HOTSPOT_LOCATOR_SCRIPT + "\n" + middleContent + afterScript;
            log.info("成功注入热点定位脚本");
            return result;
        } else {
            log.warn("未找到合适的位置注入热点定位脚本，尝试在</body>前插入");

            // 备用方案：直接在</body>前插入
            Pattern bodyPattern = Pattern.compile("(.*)(</body>)", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
            Matcher bodyMatcher = bodyPattern.matcher(htmlContent);

            if (bodyMatcher.find()) {
                String beforeBody = bodyMatcher.group(1);
                String bodyTag = bodyMatcher.group(2);

                String result = beforeBody + "\n\n" + HOTSPOT_LOCATOR_SCRIPT + "\n\n" + bodyTag;
                log.info("在</body>前成功注入热点定位脚本");
                return result;
            } else {
                log.error("无法找到</body>标签，注入失败");
                return htmlContent;
            }
        }
    }
    
    /**
     * 处理指定目录下的所有HTML文件，注入热点定位脚本
     */
    public static void processDirectory(String directoryPath) {
        try {
            Path dir = Paths.get(directoryPath);
            if (!Files.exists(dir) || !Files.isDirectory(dir)) {
                log.error("目录不存在或不是有效目录: {}", directoryPath);
                return;
            }
            
            Files.walk(dir)
                .filter(path -> path.toString().toLowerCase().endsWith(".html"))
                .forEach(Pano2VRHotspotInjector::processHtmlFile);
                
        } catch (Exception e) {
            log.error("处理目录失败: {}", directoryPath, e);
        }
    }
    
    /**
     * 处理单个HTML文件
     */
    public static void processHtmlFile(Path htmlFile) {
        try {
            log.info("处理HTML文件: {}", htmlFile);

            // 读取文件内容 - JDK 1.8兼容方式
            String content = readFileContent(htmlFile.toFile());

            // 注入脚本
            String modifiedContent = injectHotspotLocatorScript(content);

            // 如果内容有变化，写回文件
            if (!content.equals(modifiedContent)) {
                writeFileContent(htmlFile.toFile(), modifiedContent);
                log.info("已更新HTML文件: {}", htmlFile);
            }

        } catch (Exception e) {
            log.error("处理HTML文件失败: {}", htmlFile, e);
        }
    }

    /**
     * 读取文件内容 - JDK 1.8兼容方法
     */
    private static String readFileContent(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    /**
     * 写入文件内容 - JDK 1.8兼容方法
     */
    private static void writeFileContent(File file, String content) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8))) {
            writer.write(content);
        }
    }
}
